defmodule SampleApp.Relations.Users do
  # Compilation-time setup to ensure repo is available for schema inference
  # This follows the same pattern as the Drops library itself
  if Mix.env() in [:dev, :test] do
    {:ok, _} = Application.ensure_all_started(:drops)
    {:ok, _} = Application.ensure_all_started(:ecto_sql)

    # Configure and start the repo for compilation-time schema inference
    db_path = Path.expand("../../../priv/db.sqlite", __DIR__)

    Application.put_env(:sample_app, SampleApp.Repo,
      database: db_path,
      pool_size: 1,
      stacktrace: true,
      show_sensitive_data_on_connection_error: true
    )

    case SampleApp.Repo.start_link() do
      {:ok, _pid} -> :ok
      {:error, {:already_started, _pid}} -> :ok
      # Ignore errors during compilation
      _error -> :ok
    end
  end

  use Drops.Relation, repo: SampleApp.Repo, name: "users"
end
